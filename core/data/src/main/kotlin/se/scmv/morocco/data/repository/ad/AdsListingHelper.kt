package se.scmv.morocco.data.repository.ad

import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetListingAdsCountQuery
import se.scmv.morocco.GetListingAdsQuery
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.mappers.PriceSort
import se.scmv.morocco.data.mappers.toGraphQlAdTypeKey
import se.scmv.morocco.data.repository.utils.Constants
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionKeyRangeValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDownValue
import se.scmv.morocco.domain.models.orion.OrionSelectedKeysValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownItem
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownValue
import se.scmv.morocco.type.AdLocationFilter
import se.scmv.morocco.type.AdParamListTextFilter
import se.scmv.morocco.type.AdParamSingleBooleanFilter
import se.scmv.morocco.type.AdParamSingleNumericFilter
import se.scmv.morocco.type.AdParamSingleTextFilter
import se.scmv.morocco.type.AdParamsListMatchFilters
import se.scmv.morocco.type.AdParamsRangeFilter
import se.scmv.morocco.type.AdParamsSingleMatchFilters
import se.scmv.morocco.type.AdSortProperty
import se.scmv.morocco.type.AdsSearchResultSort
import se.scmv.morocco.type.ListingAdFilter
import se.scmv.morocco.type.ListingAdParamsFilters
import se.scmv.morocco.type.RangeFilter
import se.scmv.morocco.type.SortOrder
import se.scmv.morocco.domain.models.AdTypeKey as DomainAdTypeKey

object AdsListingHelper {
    private val explicitKeys = listOf(
        Constants.KEY_CATEGORY,
        Constants.KEY_KEYWORD,
        Constants.KEY_HAS_PRICE,
        Constants.KEY_PRICE_SORTING,
        Constants.KEY_HAS_IMAGE,
        Constants.KEY_CITY,
        Constants.KEY_AREA,
        Constants.KEY_DELIVERY,
        Constants.KEY_CITY_DELIVERY,
        Constants.KEY_HOT_DEAL,
        Constants.KEY_URGENT,
        Constants.KEY_ECOMMERCE,
        Constants.KEY_NEW_CONSTRUCTION,
        Constants.KEY_VERIFIED_SELLER,
        Constants.KEY_PRICE,
        Constants.KEY_SELLER_TYPE
    )

    fun buildGetListingAdsQuery(
        values: List<OrionBaseComponentValue>,
        extendSearch: Boolean,
        isNewScroll: Boolean,
        publishedAndNCAdsNextScrollId: String?,
        premiumAdsNextScrollId: String?,
    ): GetListingAdsQuery {
        val (explicitValues, implicitValues) = values.partition { it.id in explicitKeys }
        val keyStringValues = explicitValues.filterIsInstance<OrionKeyStringValue>()
        val keyBooleanValues = explicitValues.filterIsInstance<OrionKeyBooleanValue>()
        val categoryValue =
            explicitValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .firstOrNull()

        val adFilter = buildAdFilterInput(
            categoryValue = categoryValue,
            explicitValues = explicitValues,
            implicitValues = implicitValues,
            keyStringValues = keyStringValues,
            keyBooleanValues = keyBooleanValues
        )

        return GetListingAdsQuery(
            text = adFilter.text,
            categoryId = adFilter.categoryId,
            adType = adFilter.type,
            hasImage = adFilter.hasImage,
            hasPrice = adFilter.hasPrice,
            price = adFilter.price,
            location = adFilter.location,
            offersShipping = adFilter.offersShipping,
            offersShippingWithinCity = adFilter.offersShippingWithinCity,
            isHotDeal = adFilter.isHotDeal,
            isUrgent = adFilter.isUrgent,
            isEcommerce = adFilter.isEcommerce,
            isImmoneuf = adFilter.isImmoneuf,
            isVerifiedSeller = adFilter.isVerifiedSeller,
            params = adFilter.params,
            includeNewConstructionAds = Optional.presentIfNotNull(
                shouldIncludeNewConstructions(
                    categoryId = categoryValue?.category?.id?.toIntOrNull(),
                    type = categoryValue?.adTypeKey
                )
            ),
            extendPublishedAdsSearchIfNeeded = Optional.presentIfNotNull(extendSearch),
            startNewScroll = isNewScroll,
            publishedAndNCAdsNextScrollId = Optional.presentIfNotNull(publishedAndNCAdsNextScrollId),
            premiumAdsLatestScrollId = Optional.presentIfNotNull(premiumAdsNextScrollId),
            sort = Optional.present(buildAdSortProperty(keyStringValues)),
            isStore = Optional.presentIfNotNull(
                keyStringValues.firstOrNull {
                    it.id == Constants.KEY_SELLER_TYPE
                }?.let { it.value == Constants.VALUE_SELLER_TYPE_STORE }
            )
        )
    }

    fun buildGetListingAdsCountQuery(
        values: List<OrionBaseComponentValue>,
        extendSearch: Boolean
    ): GetListingAdsCountQuery {
        val (explicitValues, implicitValues) = values.partition { it.id in explicitKeys }
        val keyStringValues = explicitValues.filterIsInstance<OrionKeyStringValue>()
        val keyBooleanValues = explicitValues.filterIsInstance<OrionKeyBooleanValue>()
        val categoryValue =
            explicitValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .firstOrNull()

        return GetListingAdsCountQuery(
            adFilter = Optional.present(
                buildAdFilterInput(
                    categoryValue = categoryValue,
                    explicitValues = explicitValues,
                    implicitValues = implicitValues,
                    keyStringValues = keyStringValues,
                    keyBooleanValues = keyBooleanValues
                )
            ),
            includeNewConstructionAds = Optional.presentIfNotNull(
                shouldIncludeNewConstructions(
                    categoryId = categoryValue?.category?.id?.toIntOrNull(),
                    type = categoryValue?.adTypeKey
                )
            ),
            extendPublishedAdsSearchIfNeeded = Optional.present(extendSearch),
            isStore = Optional.presentIfNotNull(
                keyStringValues.firstOrNull {
                    it.id == Constants.KEY_SELLER_TYPE
                }?.let { it.value == Constants.VALUE_SELLER_TYPE_STORE }
            )
        )
    }

    fun buildAdFilterInput(
        categoryValue: OrionSingleSelectCategoryDropdownValue?,
        explicitValues: List<OrionBaseComponentValue>,
        implicitValues: List<OrionBaseComponentValue>,
        keyStringValues: List<OrionKeyStringValue>,
        keyBooleanValues: List<OrionKeyBooleanValue>,
    ): ListingAdFilter {

        return ListingAdFilter(
            text = Optional.presentIfNotNull(
                keyStringValues.firstOrNull { it.id == Constants.KEY_KEYWORD }?.value
            ),
            categoryId = Optional.presentIfNotNull(categoryValue?.category?.id?.toIntOrNull()),
            type = Optional.presentIfNotNull(categoryValue?.adTypeKey?.toGraphQlAdTypeKey()),
            hasPrice = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_HAS_PRICE }?.value
            ),
            hasImage = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_HAS_IMAGE }?.value
            ),
            price = Optional.presentIfNotNull(buildAdPriceFilterInput(explicitValues)),
            location = Optional.presentIfNotNull(buildAdLocationFilterInput(explicitValues)),
            offersShipping = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_DELIVERY }?.value
            ),
            offersShippingWithinCity = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_CITY_DELIVERY }?.value
            ),
            isHotDeal = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_HOT_DEAL }?.value
            ),
            isUrgent = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_URGENT }?.value
            ),
            isEcommerce = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_ECOMMERCE }?.value
            ),
            isImmoneuf = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_NEW_CONSTRUCTION }?.value
            ),
            isVerifiedSeller = Optional.presentIfNotNull(
                keyBooleanValues.firstOrNull { it.id == Constants.KEY_VERIFIED_SELLER }?.value
            ),
            params = Optional.presentIfNotNull(buildListingAdParamsFilterInput(implicitValues)),
        )
    }

    private fun buildAdLocationFilterInput(
        values: List<OrionBaseComponentValue>
    ): AdLocationFilter? {
        // If we collect values from OrionMultipleSelectSmartDropDown, we get city and area as OrionKeyStringListValue
        // If we collect values from bookmarked search and only one city/area is selected, we get them as OrionKeyStringValue
        val cityValue = values.firstOrNull { it.id == Constants.KEY_CITY }
        return when (cityValue) {
            is OrionMultipleSelectSmartDropDownValue -> {
                val cityIds = cityValue.parents.mapNotNull { it.id.toIntOrNull() }
                val areaIds = cityValue.children.mapNotNull { it.id.toIntOrNull() }
                return AdLocationFilter(
                    cityIds = Optional.presentIfNotEmpty(cityIds),
                    areaIds = Optional.presentIfNotEmpty(areaIds)
                )
            }

            is OrionSmartDropDownValue -> {
                val cityIds = listOfNotNull(cityValue.parent.id.toIntOrNull())
                val areaIds = cityValue.child
                    ?.let { listOfNotNull(it.id.toIntOrNull()) }
                return AdLocationFilter(
                    cityIds = Optional.presentIfNotEmpty(cityIds),
                    areaIds = Optional.presentIfNotEmpty(areaIds)
                )
            }

            else -> null
        }
    }

    private fun buildAdPriceFilterInput(
        values: List<OrionBaseComponentValue>
    ): RangeFilter? {
        val keyRangeValue = values.filterIsInstance<OrionKeyRangeValue>().firstOrNull {
            it.id == Constants.KEY_PRICE
        }
        return buildRangeFilterInput(keyRangeValue)
    }

    private fun buildRangeFilterInput(value: OrionKeyRangeValue?): RangeFilter? {
        val min = value?.min?.toDouble()
        val max = value?.max?.toDouble()
        return if (min != null || max != null) RangeFilter(
            greaterThanOrEqual = Optional.presentIfNotNull(min),
            lessThanOrEqual = Optional.presentIfNotNull(max)
        ) else null
    }

    private fun buildListingAdParamsFilterInput(
        values: List<OrionBaseComponentValue>,
    ): ListingAdParamsFilters? {
        val singleMatchText = mutableListOf<AdParamSingleTextFilter>()
        val singleMatchBoolean = mutableListOf<AdParamSingleBooleanFilter>()
        val singleMatchNumeric = mutableListOf<AdParamSingleNumericFilter>()
        val listMatchText = mutableListOf<AdParamListTextFilter>()
        val rangeMatch = mutableListOf<AdParamsRangeFilter>()

        values.forEach {
            when (it) {
                is OrionKeyStringValue -> {
                    singleMatchText.add(AdParamSingleTextFilter(name = it.id, value = it.value))
                }

                is OrionKeyBooleanValue -> {
                    singleMatchBoolean.add(
                        AdParamSingleBooleanFilter(name = it.id, value = it.value)
                    )
                }

                is OrionMultipleSelectSmartDropDownValue -> {
                    listMatchText.add(
                        AdParamListTextFilter(
                            name = it.id,
                            value = it.parents.map { item -> item.id })
                    )
                    listMatchText.add(
                        AdParamListTextFilter(
                            name = it.childId,
                            value = it.children.map { item -> item.id })
                    )
                }

                is OrionSmartDropDownValue -> {
                    singleMatchText.add(AdParamSingleTextFilter(name = it.id, value = it.parent.id))
                    it.child?.let { child ->
                        singleMatchText.add(
                            AdParamSingleTextFilter(
                                name = it.childId,
                                value = child.id
                            )
                        )
                    }
                }

                is OrionKeyRangeValue -> {
                    val range = buildRangeFilterInput(value = it)
                    if (range != null) {
                        rangeMatch.add(AdParamsRangeFilter(name = it.id, value = range))
                    }
                }

                is OrionSelectedKeysValue -> {
                    singleMatchNumeric.addAll(
                        it.keys.map { key -> AdParamSingleNumericFilter(name = it.id, value = 1.0) }
                    )
                }
            }
        }
        val singleMatch = if (singleMatchText.isNotEmpty() || singleMatchBoolean.isNotEmpty()) {
            Optional.present(
                AdParamsSingleMatchFilters(
                    text = Optional.presentIfNotEmpty(singleMatchText),
                    numeric = Optional.presentIfNotEmpty(singleMatchNumeric),
                    boolean = Optional.presentIfNotEmpty(singleMatchBoolean)
                )
            )
        } else Optional.absent()

        val listMatch = if (listMatchText.isNotEmpty()) {
            Optional.present(AdParamsListMatchFilters(textList = Optional.present(listMatchText)))
        } else Optional.absent()
        val range = Optional.presentIfNotEmpty(rangeMatch)
        return if (singleMatch.isPresent() || listMatch.isPresent() || range.isPresent()) {
            ListingAdParamsFilters(
                singleMatch = singleMatch,
                listMatch = listMatch,
                rangeMatch = range
            )
        } else null
    }

    private fun buildAdSortProperty(values: List<OrionBaseComponentValue>): List<AdsSearchResultSort> {
        val keyStringValue = values.filterIsInstance<OrionKeyStringValue>().firstOrNull {
            it.id == Constants.KEY_PRICE_SORTING
        }
        if (keyStringValue == null) return listOf(
            AdsSearchResultSort(
                adProperty = AdSortProperty.LIST_TIME,
                sortOrder = SortOrder.DESC
            )
        )
        return when (PriceSort.valueOf(keyStringValue.value)) {
            PriceSort.None -> AdsSearchResultSort(
                adProperty = AdSortProperty.LIST_TIME,
                sortOrder = SortOrder.DESC
            )

            PriceSort.ASC -> AdsSearchResultSort(
                adProperty = AdSortProperty.PRICE,
                sortOrder = SortOrder.ASC
            )

            PriceSort.DESC -> AdsSearchResultSort(
                adProperty = AdSortProperty.PRICE,
                sortOrder = SortOrder.DESC
            )
        }.let { listOf(it) }
    }


    fun <T : Any> Optional.Companion.presentIfNotEmpty(value: List<T?>?): Optional<List<T>> {
        val nonNullValue = value?.filterNotNull()
        return if (nonNullValue != null && nonNullValue.isNotEmpty()) {
            Optional.present(nonNullValue)
        } else {
            Optional.absent()
        }
    }

    fun <T : Any> Optional<T>.isPresent(): Boolean = this is Optional.Present

    private fun shouldIncludeNewConstructions(categoryId: Int?, type: DomainAdTypeKey?): Boolean? {
        if (categoryId == null && type == null) return null
        return categoryId in listOf(
            Constants.CONSTRUCTIONS_CATEGORY_ID_1010,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1200,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1020,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1040,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1050,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1060,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1080,
        ) && type == DomainAdTypeKey.SELL
    }

    fun buildFiltersValuesFrom(searchSuggestion: SearchSuggestion): List<OrionBaseComponentValue> {
        val list = mutableListOf<OrionBaseComponentValue>()
        val category = searchSuggestion.category
        val adTypeKey = searchSuggestion.adType?.key
        if (category != null) {
            list.add(
                OrionSingleSelectCategoryDropdownValue(
                    id = Constants.KEY_CATEGORY,
                    category = category,
                    adTypeKey = adTypeKey
                )
            )
        }
        searchSuggestion.keyword?.let { keyword ->
            list.add(OrionKeyStringValue(id = Constants.KEY_KEYWORD, value = keyword))
        }
        searchSuggestion.city?.let { city ->
            list.add(
                OrionMultipleSelectSmartDropDownValue(
                    id = Constants.KEY_CITY,
                    parents = listOf(
                        OrionSmartDropDownItem(
                            id = city.id,
                            name = city.name,
                            trackingName = city.trackingName,
                            children = emptyList()
                        )
                    ),
                    childId = Constants.KEY_AREA,
                    children = emptyList()
                )
            )
        }
        val modelKey = searchSuggestion.modelKey
        val modelId = searchSuggestion.modelId
        val brandKey = searchSuggestion.brandKey
        val brandId = searchSuggestion.brandId
        if (modelKey != null && modelId != null && brandKey != null && brandId != null) {
            list.add(
                OrionMultipleSelectSmartDropDownValue(
                    id = modelKey,
                    parents = listOf(
                        OrionSmartDropDownItem(
                            id = modelId,
                            name = "",
                            trackingName = "",
                            children = emptyList()
                        )
                    ),
                    childId = brandKey,
                    children = listOf(
                        OrionSmartDropDownItem(
                            id = brandId,
                            name = "",
                            trackingName = "",
                            children = emptyList()
                        )
                    )
                )
            )
        }
        return list
    }

    fun buildBookmarkSearchQuery(
        filtersValues: List<OrionBaseComponentValue>
    ): String = buildString {
        // Add selected category parameters
        append("lang=${LocaleManager.getCurrentLanguage()}")

        // Add other selected filters
        filtersValues.forEach { filterValue ->
            when (filterValue) {
                is OrionSingleSelectCategoryDropdownValue -> {
                    val category = "${Constants.KEY_CATEGORY}=${filterValue.category.id}"
                    val adType = filterValue.adTypeKey?.let {
                        "${Constants.KEY_AD_TYPE}=${it.name.lowercase()}"
                    }
                    if (adType != null) "$category&$adType" else category
                }

                is OrionKeyBooleanValue -> "${filterValue.id}=${filterValue.value}"
                is OrionKeyStringValue -> "${filterValue.id}=${filterValue.value}"
                is OrionKeyRangeValue -> "${filterValue.id}=${filterValue.min}-${filterValue.max}"
                is OrionMultipleSelectSmartDropDownValue -> {
                    with(filterValue) {
                        "$id=${parents.joinToString(",")}" +
                                "&$childId=${children.joinToString(",")}"
                    }
                }

                is OrionSmartDropDownValue -> {
                    with(filterValue) {
                        "$id=${parent.id}" + child?.let { "&$childId=${it.id}" }
                    }
                }

                is OrionSelectedKeysValue -> "${filterValue.id}=${filterValue.keys.joinToString(",")}"
                else -> null
            }?.let { filter ->
                append("&$filter")
            }
        }
    }

    fun buildFiltersValuesFrom(
        bookmarkedSearchQuery: String
    ): List<OrionBaseComponentValue> {
        val filtersValues = bookmarkedSearchQuery.split("&")
        return filtersValues.mapNotNull { param ->
            val parts = param.split("=")
            if (parts.size == 2) {
                val key = parts[0].removePrefix("&")
                val value = parts[1].trim()
                when {
                    key == Constants.KEY_CATEGORY -> {
                        val adTypeKey = filtersValues
                            .firstOrNull { it.startsWith("${Constants.KEY_AD_TYPE}=") }
                            ?.split("=")
                            ?.lastOrNull()
                            ?.let { DomainAdTypeKey.safeValueOf(it) }
                        OrionSingleSelectCategoryDropdownValue(
                            id = key,
                            category = Category(
                                id = value,
                                name = "",
                                icon = "",
                                trackingName = ""
                            ),
                            adTypeKey = adTypeKey
                        )
                    }

                    key == Constants.KEY_AD_TYPE || key == Constants.KEY_LANGUAGE -> null

                    value.contains("-") -> {
                        val (first: String, second: String) = value.split("-")
                        val min = first.toIntOrNull()
                        val max = second.toIntOrNull()
                        if (min != null && max != null) {
                            OrionKeyRangeValue(id = key, min = min, max = max)
                        } else null
                    }

                    value.contains(",") -> {
                        when {
                            key == Constants.KEY_CITY || key == Constants.KEY_BRAND -> {
                                val childId = if (key == Constants.KEY_CITY) {
                                    Constants.KEY_AREA
                                } else Constants.KEY_MODEL
                                val child = filtersValues.firstOrNull {
                                    it.startsWith("$childId=")
                                }?.split("=")?.lastOrNull()
                                OrionMultipleSelectSmartDropDownValue(
                                    id = key,
                                    parents = value.split(",").map {
                                        OrionSmartDropDownItem(
                                            id = it,
                                            name = "",
                                            trackingName = "",
                                            children = emptyList()
                                        )
                                    },
                                    childId = childId,
                                    children = child?.split(",")?.map {
                                        OrionSmartDropDownItem(
                                            id = it,
                                            name = "",
                                            trackingName = "",
                                            children = emptyList()
                                        )
                                    }.orEmpty()
                                )
                            }

                            else -> OrionSelectedKeysValue(id = key, keys = value.split(","))
                        }
                    }

                    value.toBooleanStrictOrNull() != null -> OrionKeyBooleanValue(
                        id = key,
                        value = value.toBooleanStrict()
                    )

                    value.isNotBlank() -> OrionKeyStringValue(id = key, value = value)
                    else -> null
                }
            } else null
        }
    }
}