package se.scmv.morocco.data.repository.ad

import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Test
import se.scmv.morocco.GetAdForEditQuery
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AccountInfo
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringItem
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionMediaUploaderValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownItem
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownValue
import se.scmv.morocco.type.AdTypeKey
import se.scmv.morocco.domain.models.AdTypeKey as DomainAdTypeKey

class AdInsertHelperTest {

    @Test
    fun buildSubmitAdInput() {
        // Setup
        val adId = "test-ad-123"
        val account = Account.Connected.Private(
            contact = AccountInfo(
                accountId = "123",
                name = "Test User",
                email = "<EMAIL>",
                phone = "*********",
                location = null,
                creationDate = ""
            ),
            isPhoneHidden = true
        )
        val values = listOf(
            OrionSingleSelectCategoryDropdownValue(
                id = "category",
                category = Category("cars", "Cars", "cars", "Cars"),
                adTypeKey = DomainAdTypeKey.SELL
            ),
            OrionKeyStringValue(id = "title", value = "My Car"),
            OrionKeyStringValue(id = "description", value = "A nice car"),
            OrionKeyStringValue(id = "price", value = "10000"),
            OrionKeyStringValue(id = "phone", value = "*********"),
            OrionKeyBooleanValue(id = "phone_hidden", value = true),
            OrionSmartDropDownValue(
                id = "city",
                parent = OrionSmartDropDownItem(
                    id = "Rabat",
                    name = "Rabat",
                    trackingName = "rabat",
                    children = emptyList()
                ),
                childId = "area",
                child = OrionSmartDropDownItem(
                    id = "Downtown",
                    name = "Downtown",
                    trackingName = "downtown",
                    children = emptyList()
                )
            ),
            OrionKeyStringValue(id = "address", value = "123 Main St"),
            OrionKeyStringValue(id = "color", value = "red"), // Implicit value
            OrionKeyBooleanValue(id = "negotiable", value = true), // Implicit value
            OrionMediaUploaderValue(
                id = "images",
                medias = listOf(OrionKeyStringItem(id = "img1", name = "image.jpg"))
            )
        )

        // Execute
        val result = AdInsertHelper.buildSubmitAdInput(adId, values, account)

        // Verify
        assertEquals("test-ad-123", result.adId.getOrNull())
        assertEquals("cars", result.category)
        assertEquals(AdTypeKey.SELL, result.type)
        assertEquals(10000, result.price)
        assertEquals("My Car", result.title)
        assertEquals("A nice car", result.description)
        assertEquals("Test User", result.name)
        assertEquals("*********", result.phone)
        assertTrue(result.phoneHidden)
        assertEquals("Rabat", result.location.city)
        assertEquals("Downtown", result.location.area.getOrNull())
        assertEquals("123 Main St", result.location.address.getOrNull())

        // Check params
        assertEquals(1, result.params.boolean.size)
        assertEquals("negotiable", result.params.boolean[0].id)
        assertTrue(result.params.boolean[0].value)

        assertEquals(1, result.params.listKey.size)
        assertEquals("color", result.params.listKey[0].id)
        assertEquals("red", result.params.listKey[0].value)

        // Check media
        assertEquals(1, result.media.getOrNull()?.images?.size)
        assertEquals("img1", result.media.getOrNull()?.images?.get(0)?.id)
    }

    @Test
    fun buildAdEditValues() {
        // Setup
        val adForEdit = getMockAdForEdit()

        // Execute
        val result = AdInsertHelper.buildAdEditValues(adForEdit)

        // Verify
        assertEquals(13, result.size)

        // Check explicit values
        val categoryValue = result
            .filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
            .first()
        assertEquals("category", categoryValue.id)
        assertEquals("cars", categoryValue.category.id)
        assertEquals(DomainAdTypeKey.SELL, categoryValue.adTypeKey)

        val cityValue = result.filterIsInstance<OrionSmartDropDownValue>().first()
        assertTrue(cityValue.id == "city" && cityValue.parent.id == "Casablanca")
        assertTrue(cityValue.childId == "area" && cityValue.child?.id == "City Center")

        val stringValues = result.filterIsInstance<OrionKeyStringValue>()
        assertTrue(stringValues.any { it.id == "title" && it.value == "Used Car" })
        assertTrue(stringValues.any { it.id == "description" && it.value == "Well maintained" })
        assertTrue(stringValues.any { it.id == "price" && it.value == "15000" })
        assertTrue(stringValues.any { it.id == "name" && it.value == "John Doe" })
        assertTrue(stringValues.any { it.id == "phone" && it.value == "*********" })
        assertTrue(stringValues.any { it.id == "address" && it.value == "123 Main St" })

        val phoneHidden = result.filterIsInstance<OrionKeyBooleanValue>()
            .first { it.id == "phone_hidden" }
        assertFalse(phoneHidden.value)

        // Check media values
        val imageUpload = result.filterIsInstance<OrionMediaUploaderValue>()
            .first { it.id == "images" }
        assertEquals(1, imageUpload.medias.size)
        assertEquals("img1", imageUpload.medias[0].id)
        assertTrue(imageUpload.medias[0].name.contains("path/to/image.jpg"))

        val videoUpload = result.filterIsInstance<OrionMediaUploaderValue>()
            .first { it.id == "videos" }
        assertEquals(1, videoUpload.medias.size)
        assertEquals("vid1", videoUpload.medias[0].id)
        assertTrue(videoUpload.medias[0].name.contains("path/to/video.mp4"))

        // Check params
        val colorParam = result.filterIsInstance<OrionKeyStringValue>()
            .first { it.id == "color" }
        assertEquals("blue", colorParam.value)

        val isNegotiableParam = result.filterIsInstance<OrionKeyBooleanValue>()
            .first { it.id == "negotiable" }
        assertTrue(isNegotiableParam.value)
    }


    private fun getMockAdForEdit(): GetAdForEditQuery.GetAdForEdit {
        return GetAdForEditQuery.GetAdForEdit(
            category = "cars",
            price = 15000,
            type = AdTypeKey.SELL,
            title = "Used Car",
            description = "Well maintained",
            name = "John Doe",
            phone = "*********",
            phoneHidden = false,
            location = GetAdForEditQuery.Location(
                city = "Casablanca",
                area = "City Center",
                address = "123 Main St"
            ),
            params = listOf(
                GetAdForEditQuery.Param(
                    __typename = "AdParam",
                    onAdEditListKeyParam = GetAdForEditQuery.OnAdEditListKeyParam(
                        id = "color",
                        keyValue = "blue"
                    ),
                    onAdEditBooleanParam = null,
                    onAdEditNumericParam = null
                ),
                GetAdForEditQuery.Param(
                    __typename = "AdParam",
                    onAdEditListKeyParam = null,
                    onAdEditBooleanParam = GetAdForEditQuery.OnAdEditBooleanParam(
                        id = "negotiable",
                        booleanValue = true
                    ),
                    onAdEditNumericParam = null
                )
            ),
            isSifm = null,
            media = GetAdForEditQuery.Media(
                images = listOf(
                    GetAdForEditQuery.Image(
                        id = "img1",
                        defaultPath = "path/to/image.jpg"
                    )
                ),
                videos = listOf(
                    GetAdForEditQuery.Video(
                        id = "vid1",
                        defaultPath = "path/to/video.mp4"
                    )
                )
            )
        )
    }
}