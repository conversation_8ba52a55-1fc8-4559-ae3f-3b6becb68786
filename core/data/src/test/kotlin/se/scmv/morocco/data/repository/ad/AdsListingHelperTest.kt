package se.scmv.morocco.data.repository.ad

import com.apollographql.apollo3.api.Optional
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.mappers.PriceSort
import se.scmv.morocco.data.repository.ad.AdsListingHelper.isPresent
import se.scmv.morocco.data.repository.utils.Constants
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionKeyRangeValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDownValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownItem
import se.scmv.morocco.type.AdSortProperty
import se.scmv.morocco.type.SortOrder

class AdsListingHelperTest {

    @Before
    fun setUp() {
        mockkStatic(LocaleManager::class)
        every { LocaleManager.getCurrentLanguage() } returns "fr"
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    // Test data helpers
    private fun createCategoryValue(
        categoryId: String = "123",
        adTypeKey: AdTypeKey? = AdTypeKey.SELL
    ) = OrionSingleSelectCategoryDropdownValue(
        id = Constants.KEY_CATEGORY,
        category = Category(
            id = categoryId,
            name = "Test Category",
            icon = "icon",
            trackingName = "test_category"
        ),
        adTypeKey = adTypeKey
    )

    private fun createKeywordValue(keyword: String = "test keyword") =
        OrionKeyStringValue(id = Constants.KEY_KEYWORD, value = keyword)

    private fun createBooleanValue(key: String, value: Boolean) =
        OrionKeyBooleanValue(id = key, value = value)

    private fun createRangeValue(key: String, min: Int, max: Int) =
        OrionKeyRangeValue(id = key, min = min, max = max)

    private fun createSmartDropDownItem(id: String, name: String = "Item $id") =
        OrionSmartDropDownItem(
            id = id,
            name = name,
            trackingName = "item_$id",
            children = emptyList()
        )

    @Test
    fun `buildGetListingAdsQuery should build query with basic values`() {
        // Given
        val categoryValue = createCategoryValue()
        val keywordValue = createKeywordValue()
        val values = listOf(categoryValue, keywordValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = true,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = "scroll123",
            premiumAdsNextScrollId = "premium456"
        )

        // Then
        assertEquals(Optional.present("test keyword"), result.text)
        assertEquals(Optional.present(123), result.categoryId)
        assertEquals(Optional.present(se.scmv.morocco.type.AdTypeKey.SELL), result.adType)
        assertEquals(Optional.present(true), result.extendPublishedAdsSearchIfNeeded)
        assertEquals(false, result.startNewScroll)
        assertEquals(Optional.present("scroll123"), result.publishedAndNCAdsNextScrollId)
        assertEquals(Optional.present("premium456"), result.premiumAdsLatestScrollId)
    }

    @Test
    fun `buildGetListingAdsQuery should handle boolean filters`() {
        // Given
        val values = listOf(
            createBooleanValue(Constants.KEY_HAS_PRICE, true),
            createBooleanValue(Constants.KEY_HAS_IMAGE, false),
            createBooleanValue(Constants.KEY_DELIVERY, true),
            createBooleanValue(Constants.KEY_HOT_DEAL, true),
            createBooleanValue(Constants.KEY_URGENT, false)
        )

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = true,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertEquals(Optional.present(true), result.hasPrice)
        assertEquals(Optional.present(false), result.hasImage)
        assertEquals(Optional.present(true), result.offersShipping)
        assertEquals(Optional.present(true), result.isHotDeal)
        assertEquals(Optional.present(false), result.isUrgent)
        assertEquals(Optional.present(false), result.extendPublishedAdsSearchIfNeeded)
        assertEquals(true, result.startNewScroll)
        assertEquals(Optional.absent<String>(), result.publishedAndNCAdsNextScrollId)
        assertEquals(Optional.absent<String>(), result.premiumAdsLatestScrollId)
    }

    @Test
    fun `buildGetListingAdsQuery should handle seller type store`() {
        // Given
        val sellerTypeValue = OrionKeyStringValue(
            id = Constants.KEY_SELLER_TYPE,
            value = Constants.VALUE_SELLER_TYPE_STORE
        )
        val values = listOf(sellerTypeValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertEquals(Optional.present(true), result.isStore)
    }

    @Test
    fun `buildGetListingAdsQuery should handle seller type private`() {
        // Given
        val sellerTypeValue = OrionKeyStringValue(
            id = Constants.KEY_SELLER_TYPE,
            value = Constants.VALUE_SELLER_TYPE_PRIVATE
        )
        val values = listOf(sellerTypeValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertEquals(Optional.present(false), result.isStore)
    }

    @Test
    fun `buildGetListingAdsQuery should include new construction ads for valid categories`() {
        // Given
        val categoryValue = createCategoryValue(
            categoryId = Constants.CONSTRUCTIONS_CATEGORY_ID_1010.toString(),
            adTypeKey = AdTypeKey.SELL
        )
        val values = listOf(categoryValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertEquals(Optional.present(true), result.includeNewConstructionAds)
    }

    @Test
    fun `buildGetListingAdsQuery should not include new construction ads for invalid categories`() {
        // Given
        val categoryValue = createCategoryValue(
            categoryId = "999",
            adTypeKey = AdTypeKey.SELL
        )
        val values = listOf(categoryValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertEquals(Optional.present(false), result.includeNewConstructionAds)
    }

    @Test
    fun `buildGetListingAdsCountQuery should build count query correctly`() {
        // Given
        val categoryValue = createCategoryValue()
        val keywordValue = createKeywordValue()
        val hasPriceValue = createBooleanValue(Constants.KEY_HAS_PRICE, true)
        val values = listOf(categoryValue, keywordValue, hasPriceValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsCountQuery(
            values = values,
            extendSearch = true
        )

        // Then
        assertNotNull(result.adFilter.getOrNull())
        val adFilter = result.adFilter.getOrNull()!!
        assertEquals(Optional.present("test keyword"), adFilter.text)
        assertEquals(Optional.present(123), adFilter.categoryId)
        assertEquals(Optional.present(true), adFilter.hasPrice)
        assertEquals(Optional.present(true), result.extendPublishedAdsSearchIfNeeded)
    }



    @Test
    fun `buildGetListingAdsQuery should handle price range filter`() {
        // Given
        val priceRangeValue = createRangeValue(Constants.KEY_PRICE, 1000, 5000)
        val values = listOf(priceRangeValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertNotNull(result.price.getOrNull())
        val priceFilter = result.price.getOrNull()!!
        assertEquals(Optional.present(1000.0), priceFilter.greaterThanOrEqual)
        assertEquals(Optional.present(5000.0), priceFilter.lessThanOrEqual)
    }

    @Test
    fun `buildGetListingAdsQuery should handle location filter with smart dropdown`() {
        // Given
        val cityItem = createSmartDropDownItem("123", "Casablanca")
        val areaItem = createSmartDropDownItem("456", "Maarif")
        val locationValue = OrionMultipleSelectSmartDropDownValue(
            id = Constants.KEY_CITY,
            childId = Constants.KEY_AREA,
            parents = listOf(cityItem),
            children = listOf(areaItem)
        )
        val values = listOf(locationValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertNotNull(result.location.getOrNull())
        val locationFilter = result.location.getOrNull()!!
        assertEquals(Optional.present(listOf(123)), locationFilter.cityIds)
        assertEquals(Optional.present(listOf(456)), locationFilter.areaIds)
    }

    @Test
    fun `buildFiltersValuesFrom SearchSuggestion should create correct filter values`() {
        // Given
        val category = Category(
            id = "123",
            name = "Electronics",
            icon = "icon",
            trackingName = "electronics"
        )
        val searchSuggestion = SearchSuggestion(
            uuid = "uuid",
            keyword = "iPhone",
            category = category,
            adType = mockk {
                every { key } returns AdTypeKey.SELL
            },
            city = City(
                id = "456",
                name = "Casablanca",
                trackingName = "casablanca"
            ),
            modelKey = "model",
            modelId = "model123",
            modelName = "iPhone 12",
            brandKey = "brand",
            brandId = "brand456",
            brandName = "Apple",
            isHistory = false
        )

        // When
        val result = AdsListingHelper.buildFiltersValuesFrom(searchSuggestion)

        // Then
        assertEquals(4, result.size)

        // Check category value
        val categoryValue = result.filterIsInstance<OrionSingleSelectCategoryDropdownValue>().first()
        assertEquals(Constants.KEY_CATEGORY, categoryValue.id)
        assertEquals("123", categoryValue.category.id)
        assertEquals(AdTypeKey.SELL, categoryValue.adTypeKey)

        // Check keyword value
        val keywordValue = result.filterIsInstance<OrionKeyStringValue>()
            .first { it.id == Constants.KEY_KEYWORD }
        assertEquals("iPhone", keywordValue.value)

        // Check location value
        val locationValue = result.filterIsInstance<OrionMultipleSelectSmartDropDownValue>()
            .first { it.id == Constants.KEY_CITY }
        assertEquals("456", locationValue.parents.first().id)

        // Check model/brand value
        val modelValue = result.filterIsInstance<OrionMultipleSelectSmartDropDownValue>()
            .first { it.id == Constants.KEY_BRAND }
        assertEquals("brand456", modelValue.parents.first().id)
        assertEquals("model123", modelValue.children.first().id)
    }

    @Test
    fun `buildFiltersValuesFrom SearchSuggestion should handle minimal data`() {
        // Given
        val searchSuggestion = SearchSuggestion(
            uuid = "uuid",
            keyword = "test",
            category = null,
            adType = null,
            city = null,
            modelKey = null,
            modelId = null,
            modelName = null,
            brandKey = null,
            brandId = null,
            brandName = null,
            isHistory = false
        )

        // When
        val result = AdsListingHelper.buildFiltersValuesFrom(searchSuggestion)

        // Then
        assertEquals(1, result.size)
        val keywordValue = result.first() as OrionKeyStringValue
        assertEquals(Constants.KEY_KEYWORD, keywordValue.id)
        assertEquals("test", keywordValue.value)
    }

    @Test
    fun `buildBookmarkSearchQuery should create correct query string`() {
        // Given
        val categoryValue = createCategoryValue()
        val keywordValue = createKeywordValue("search term")
        val hasPriceValue = createBooleanValue(Constants.KEY_HAS_PRICE, true)
        val priceRangeValue = createRangeValue(Constants.KEY_PRICE, 100, 500)
        val values = listOf(categoryValue, keywordValue, hasPriceValue, priceRangeValue)

        // When
        val result = AdsListingHelper.buildBookmarkSearchQuery(values)

        // Then
        assertTrue(result.contains("lang=fr"))
        assertTrue(result.contains("${Constants.KEY_CATEGORY}=123"))
        assertTrue(result.contains("${Constants.KEY_AD_TYPE}=sell"))
        assertTrue(result.contains("${Constants.KEY_KEYWORD}=search term"))
        assertTrue(result.contains("${Constants.KEY_HAS_PRICE}=true"))
        assertTrue(result.contains("${Constants.KEY_PRICE}=100-500"))
    }

    @Test
    fun `buildBookmarkSearchQuery should handle smart dropdown values`() {
        // Given
        val cityItem1 = createSmartDropDownItem("123")
        val cityItem2 = createSmartDropDownItem("456")
        val areaItem = createSmartDropDownItem("789")
        val locationValue = OrionMultipleSelectSmartDropDownValue(
            id = Constants.KEY_CITY,
            childId = Constants.KEY_AREA,
            parents = listOf(cityItem1, cityItem2),
            children = listOf(areaItem)
        )
        val values = listOf(locationValue)

        // When
        val result = AdsListingHelper.buildBookmarkSearchQuery(values)

        // Then
        assertTrue(result.contains("${Constants.KEY_CITY}=123,456"))
        assertTrue(result.contains("${Constants.KEY_AREA}=789"))
    }

    @Test
    fun `buildFiltersValuesFrom bookmarked query should parse correctly`() {
        // Given
        val query = "lang=fr&${Constants.KEY_CATEGORY}=123&${Constants.KEY_AD_TYPE}=sell&${Constants.KEY_KEYWORD}=test&${Constants.KEY_HAS_PRICE}=true"

        // When
        val result = AdsListingHelper.buildFiltersValuesFrom(query)

        // Then
        assertEquals(3, result.size)

        // Check category value
        val categoryValue = result.filterIsInstance<OrionSingleSelectCategoryDropdownValue>().first()
        assertEquals("123", categoryValue.category.id)
        assertEquals(AdTypeKey.SELL, categoryValue.adTypeKey)

        // Check keyword value
        val keywordValue = result.filterIsInstance<OrionKeyStringValue>()
            .first { it.id == Constants.KEY_KEYWORD }
        assertEquals("test", keywordValue.value)

        // Check boolean value
        val booleanValue = result.filterIsInstance<OrionKeyBooleanValue>().first()
        assertEquals(Constants.KEY_HAS_PRICE, booleanValue.id)
        assertEquals(true, booleanValue.value)
    }

    @Test
    fun `buildFiltersValuesFrom bookmarked query should handle range values`() {
        // Given
        val query = "lang=fr&${Constants.KEY_PRICE}=100-500"

        // When
        val result = AdsListingHelper.buildFiltersValuesFrom(query)

        // Then
        assertEquals(1, result.size)
        val rangeValue = result.first() as OrionKeyRangeValue
        assertEquals(Constants.KEY_PRICE, rangeValue.id)
        assertEquals(100, rangeValue.min)
        assertEquals(500, rangeValue.max)
    }

    @Test
    fun `buildFiltersValuesFrom bookmarked query should handle malformed parameters`() {
        // Given
        val query = "lang=fr&malformed&${Constants.KEY_KEYWORD}=test&incomplete="

        // When
        val result = AdsListingHelper.buildFiltersValuesFrom(query)

        // Then
        assertEquals(1, result.size)
        val keywordValue = result.first() as OrionKeyStringValue
        assertEquals(Constants.KEY_KEYWORD, keywordValue.id)
        assertEquals("test", keywordValue.value)
    }

    @Test
    fun `Optional presentIfNotEmpty should return present for non-empty list`() {
        // Given
        val list = listOf("item1", "item2", null, "item3")

        // When
        val result = AdsListingHelper.run { Optional.presentIfNotEmpty(list) }

        // Then
        assertTrue(result.isPresent())
        assertEquals(listOf("item1", "item2", "item3"), result.getOrNull())
    }

    @Test
    fun `Optional presentIfNotEmpty should return absent for empty list`() {
        // Given
        val list = emptyList<String>()

        // When
        val result = AdsListingHelper.run { Optional.presentIfNotEmpty(list) }

        // Then
        assertFalse(result.isPresent())
    }

    @Test
    fun `Optional presentIfNotEmpty should return absent for null list`() {
        // Given
        val list: List<String>? = null

        // When
        val result = AdsListingHelper.run { Optional.presentIfNotEmpty(list) }

        // Then
        assertFalse(result.isPresent())
    }

    @Test
    fun `Optional presentIfNotEmpty should return absent for list with only nulls`() {
        // Given
        val list = listOf<String?>(null, null, null)

        // When
        val result = AdsListingHelper.run { Optional.presentIfNotEmpty(list) }

        // Then
        assertFalse(result.isPresent())
    }

    @Test
    fun `Optional isPresent should return true for present optional`() {
        // Given
        val optional = Optional.present("value")

        // When
        val result = AdsListingHelper.run { optional.isPresent() }

        // Then
        assertTrue(result)
    }

    @Test
    fun `Optional isPresent should return false for absent optional`() {
        // Given
        val optional = Optional.absent<String>()

        // When
        val result = AdsListingHelper.run { optional.isPresent() }

        // Then
        assertFalse(result)
    }

    @Test
    fun `buildGetListingAdsQuery should handle price sorting - None`() {
        // Given
        val priceSortValue = OrionKeyStringValue(
            id = Constants.KEY_PRICE_SORTING,
            value = PriceSort.None.name
        )
        val values = listOf(priceSortValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertNotNull(result.sort.getOrNull())
        val sortList = result.sort.getOrNull()!!
        assertEquals(1, sortList.size)
        assertEquals(AdSortProperty.LIST_TIME, sortList.first()?.adProperty)
        assertEquals(SortOrder.DESC, sortList.first()?.sortOrder)
    }

    @Test
    fun `buildGetListingAdsQuery should handle price sorting - ASC`() {
        // Given
        val priceSortValue = OrionKeyStringValue(
            id = Constants.KEY_PRICE_SORTING,
            value = PriceSort.ASC.name
        )
        val values = listOf(priceSortValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertNotNull(result.sort.getOrNull())
        val sortList = result.sort.getOrNull()!!
        assertEquals(1, sortList.size)
        assertEquals(AdSortProperty.PRICE, sortList.first()?.adProperty)
        assertEquals(SortOrder.ASC, sortList.first()?.sortOrder)
    }

    @Test
    fun `buildGetListingAdsQuery should handle price sorting - DESC`() {
        // Given
        val priceSortValue = OrionKeyStringValue(
            id = Constants.KEY_PRICE_SORTING,
            value = PriceSort.DESC.name
        )
        val values = listOf(priceSortValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertNotNull(result.sort.getOrNull())
        val sortList = result.sort.getOrNull()!!
        assertEquals(1, sortList.size)
        assertEquals(AdSortProperty.PRICE, sortList.first()?.adProperty)
        assertEquals(SortOrder.DESC, sortList.first()?.sortOrder)
    }

    @Test
    fun `buildGetListingAdsQuery should default to LIST_TIME DESC when no price sorting`() {
        // Given
        val values = emptyList<OrionBaseComponentValue>()

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertNotNull(result.sort.getOrNull())
        val sortList = result.sort.getOrNull()!!
        assertEquals(1, sortList.size)
        assertEquals(AdSortProperty.LIST_TIME, sortList.first()?.adProperty)
        assertEquals(SortOrder.DESC, sortList.first()?.sortOrder)
    }
    @Test
    fun `buildGetListingAdsQuery should handle implicit values for params`() {
        // Given - implicit values are those not in explicitKeys list
        val implicitStringValue = OrionKeyStringValue(id = "custom_param", value = "custom_value")
        val implicitBooleanValue = OrionKeyBooleanValue(id = "custom_bool", value = true)
        val values = listOf(implicitStringValue, implicitBooleanValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then - implicit values should be included in params
        assertNotNull(result.params.getOrNull())
        val params = result.params.getOrNull()!!
        assertNotNull(params.singleMatch.getOrNull())
    }





    @Test
    fun `buildGetListingAdsQuery should handle all construction category IDs`() {
        val constructionCategoryIds = listOf(
            Constants.CONSTRUCTIONS_CATEGORY_ID_1010,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1200,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1020,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1040,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1050,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1060,
            Constants.CONSTRUCTIONS_CATEGORY_ID_1080
        )

        constructionCategoryIds.forEach { categoryId ->
            // Given
            val categoryValue = createCategoryValue(
                categoryId = categoryId.toString(),
                adTypeKey = AdTypeKey.SELL
            )
            val values = listOf(categoryValue)

            // When
            val result = AdsListingHelper.buildGetListingAdsQuery(
                values = values,
                extendSearch = false,
                isNewScroll = false,
                publishedAndNCAdsNextScrollId = null,
                premiumAdsNextScrollId = null
            )

            // Then
            assertEquals(
                "Category $categoryId should include new construction ads",
                Optional.present(true),
                result.includeNewConstructionAds
            )
        }
    }

    @Test
    fun `buildGetListingAdsQuery should not include new construction for non-SELL ad types`() {
        // Given
        val categoryValue = createCategoryValue(
            categoryId = Constants.CONSTRUCTIONS_CATEGORY_ID_1010.toString(),
            adTypeKey = AdTypeKey.RENT
        )
        val values = listOf(categoryValue)

        // When
        val result = AdsListingHelper.buildGetListingAdsQuery(
            values = values,
            extendSearch = false,
            isNewScroll = false,
            publishedAndNCAdsNextScrollId = null,
            premiumAdsNextScrollId = null
        )

        // Then
        assertEquals(Optional.present(false), result.includeNewConstructionAds)
    }
}