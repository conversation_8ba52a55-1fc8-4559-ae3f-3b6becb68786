<resources xmlns:ns1="http://schemas.android.com/tools">
    <style name="Theme.App.Starting" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/splashColor</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_splash_screen</item>
        <item name="postSplashScreenTheme">@style/Theme.MaterialComponents.DayNight.NoActionBar</item>
    </style>

    <style name="Theme.Avito" parent="@style/Theme.MaterialComponents.DayNight.NoActionBar" />


    <!--    Remove all the following styles once we remove the layouts-->
    <style name="OrionTextMediumBold" parent="OrionTextLargeBold">
        <item name="android:textSize">@dimen/orion_text_medium</item>
        <item name="android:fontFamily">@font/rubik</item>
    </style>
    <style name="OrionTextMediumBoldDark" parent="OrionTextLargeBold">
        <item name="android:textColor">@color/orionTextColorDark</item>
        <item name="android:textSize">@dimen/orion_text_medium</item>
        <item name="android:textStyle">bold</item>

    </style>
    <style name="ThemeOverlay.App.MaterialAlertDialog" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="colorPrimary">@color/avitoBlue</item>
    </style>

    <style name="dialogText">
        <item name="android:textColor">@color/delete_ad_text</item>
        <item name="android:textSize">13sp</item>
    </style>

    <style name="ShapeAppearanceOverlay.App.CornerSize50Percent" parent="">
        <item name="cornerSize">50%</item>
    </style>

    <style name="RoundedShapeAppearanceOverlay8dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <style name="TopRoundedShapeAppearanceOverlay8dp">
        <item name="cornerFamilyTopLeft">rounded</item>
        <item name="cornerFamilyTopRight">rounded</item>
        <item name="cornerSizeTopLeft">8dp</item>
        <item name="cornerSizeTopRight">8dp</item>
    </style>

    <style name="TopRoundedShapeAppearanceOverlay12dp">
        <item name="cornerFamilyTopLeft">rounded</item>
        <item name="cornerFamilyTopRight">rounded</item>
        <item name="cornerSizeTopLeft">12dp</item>
        <item name="cornerSizeTopRight">12dp</item>
    </style>


    <style name="AppTheme.White" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorAccent">@color/white</item>
    </style>


    <style name="uploadCheckBox" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorControlNormal">#fff</item>
        <item name="colorControlActivated">#42a5f5</item>
    </style>

    <!-- drawer arrow style -->
    <style name="DrawerArrowStyle" parent="Widget.AppCompat.DrawerArrowToggle">
        <item name="spinBars">true</item>
        <item name="color">@android:color/black</item>
    </style>

    <!-- Alert dialogs style -->
    <style name="AlertDialogCustom" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorAccent">@color/alert_dialog_btn_color</item>
        <item name="materialButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
    </style>

    <!-- toolbar title and menu text -->
    <style name="ToolbarTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:textColorPrimary">@color/gray</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:background">@color/action_bar_color</item>
    </style>

    <!-- ad view and listing -->
    <style name="AdTitleText">
        <item name="android:textSize">14sp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">#535353</item>
        <item name="android:maxLines">2</item>
        <item name="android:fontFamily">@font/rubik</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- search and filters -->

    <style name="FieldsTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/content_color</item>
    </style>

    <style name="ContentTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/content_color</item>
    </style>

    <style name="RadioButtonStyle">
        <item name="colorAccent">#4a4a4a</item>
        <item name="android:textColorSecondary">#4a4a4a</item>
    </style>

    <style name="AvitoSpinner">
        <item name="android:dropDownHorizontalOffset">5dp</item>
        <item name="android:dropDownHeight">wrap_content</item>
        <item name="android:layout_height">@dimen/orion_spinner_height</item>
        <item name="android:divider">@android:color/black</item>
        <item name="android:dividerHeight">1dp</item>
        <item name="android:spinnerMode">dropdown</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/spinner_frame_normal</item>
        <item name="android:popupBackground">@android:color/white</item>
        <item name="android:textColor">@color/gray</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/text_size_small</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:itemPadding">4dp</item>
        <item name="android:paddingBottom">4dp</item>
    </style>


    <style name="AvitoTowns">
        <item name="android:dropDownHorizontalOffset">5dp</item>
        <item name="android:dropDownHeight">wrap_content</item>
        <item name="android:divider">@android:color/black</item>
        <item name="android:dividerHeight">1dp</item>
        <item name="android:spinnerMode">dropdown</item>
        <item name="android:background">@drawable/spinner_frame_normal</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:popupBackground">@android:color/white</item>
        <item name="android:textColor">@color/content_color</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">13sp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:paddingLeft">4dp</item>
        <item name="android:paddingRight">28dp</item>
    </style>

    <style name="AvitoAutocompleteItem">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/avito_primary_text_color</item>
        <item name="android:gravity">start|center_vertical</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>

    <!--This Spinner is used for monetisation and is intended to be generalized for all spinner -->

    <!-- login -->

    <!-- ad insert -->
    <style name="AiFieldTitle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/gray</item>
        <item name="android:fontFamily">@font/rubik_medium</item>
        <item name="colorControlActivated">@color/login_btn_bg</item>
        <item name="colorControlHighlight">@color/login_btn_bg</item>
    </style>

    <style name="AiTextField">
        <!-- Hint color and label color in FALSE state -->
        <item name="android:textColorHint">@color/color_control_normal</item>
        <item name="android:textColor">@color/edit_field</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!--Popup menu style-->
    <style name="PopupMenu" parent="@android:style/TextAppearance.Widget.IconMenu.Item">
        <item name="android:popupBackground">@android:color/white</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="ef_BaseTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="windowActionModeOverlay">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="ef_CustomToolbarTheme" parent="ThemeOverlay.MaterialComponents.Dark.ActionBar">
        <item name="android:textColorPrimary">@color/ef_colorPrimary</item>
        <item name="actionMenuTextColor">@color/ef_colorPrimary</item>
        <item name="navigationIcon">@drawable/ic_arrow_back_black</item>
    </style>


    <style name="ef_Toolbar.TitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/colorDarkerGray</item>
        <item name="fontFamily">@font/rubik</item>
    </style>

    <!-- CollapsingToolbar style-->
    <style name="CollapseCollapsingToolbarLayoutText">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/cardview_light_background</item>
    </style>

    <style name="ExpandCollapsingToolbarLayoutText">
        <item name="android:textSize">1sp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="AppTheme.ToolbarTheme" parent="ThemeOverlay.MaterialComponents.Light">
        <item name="android:fontFamily">@font/rubik_medium</item>
        <item name="android:textColorPrimary">#555</item>
    </style>

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.MaterialComponents.Light" />

    <!--Toolbar edit appearance -->
    <style name="Toolbar.TitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/rubik_bold</item>
    </style>

    <style name="deleteDialogText">
        <item name="android:textColor">@color/delete_ad_text</item>
        <item name="android:textSize">13sp</item>
    </style>

    <!--Error appearance for textInputLayout-->


    <!-- Image Picker Theme -->
    <style name="ImagePickerTheme" parent="ef_BaseTheme">
        <item name="colorPrimary">@color/action_bar_color</item>
        <item name="colorPrimaryDark">@color/status_bar_color</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>


    <style name="BottomSheetDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/bottomSheetStyleWrapper</item>
    </style>

    <style name="bottomSheetStyleWrapper" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="bottomNavigationText">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">55dp</item>
        <item name="android:textColor">@color/selector_navigation_menu_item</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center|bottom</item>
        <item name="android:fontFamily">@font/rubik_medium</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:longClickable">false</item>
        <item name="android:capitalize">sentences</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:textSize">11sp</item>
    </style>


    <style name="Braze.Feed.List">
        <item name="android:background">@color/appBackground</item>
        <item name="android:cacheColorHint">@color/appBackground</item>
        <item name="android:divider">@android:color/transparent</item>
        <item name="android:dividerHeight">16.0dp</item>
        <item name="android:paddingLeft">12.5dp</item>
        <item name="android:paddingRight">5.0dp</item>
        <item name="android:scrollbarStyle">outsideInset</item>
    </style>

    <style name="notificationBadgeStyle">
        <item name="android:layout_width">16dp</item>
        <item name="android:layout_marginBottom">28dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="android:layout_marginLeft">24dp</item>
        <item name="android:layout_marginRight">8dp</item>
        <item name="android:layout_marginStart">24dp</item>
        <item name="android:layout_height">16dp</item>
        <item name="android:visibility">gone</item>
        <item name="android:textSize">7sp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:fontFamily">@font/rubik_medium</item>
        <item name="android:background">@drawable/badge_rounded_with_border</item>
    </style>

    <style name="CounterBadgeStyle">
        <item name="android:layout_width">14dp</item>
        <item name="android:layout_height">14dp</item>
        <item name="android:visibility">gone</item>
        <item name="android:textSize">8sp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:fontFamily">@font/rubik_medium</item>
        <item name="android:background">@drawable/badge_rounded</item>
    </style>

    <style name="screenTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:fontFamily">@font/rubik_medium</item>
        <item name="android:textColor">#303030</item>
        <item name="android:textSize">18sp</item>
        <item name="android:layout_marginLeft">16dp</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginRight">16dp</item>
    </style>

    <style name="emptyViewTitle">
        <item name="android:layout_width">280dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/no_textview</item>
        <item name="android:textSize">@dimen/no_results_text_size</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="emptyViewDescription">
        <item name="android:layout_width">260dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">19dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/no_textview</item>
        <item name="android:textSize">@dimen/ai_fields_text_size</item>
    </style>

    <style name="emptyViewImage">
        <item name="android:layout_width">206dp</item>
        <item name="android:layout_height">190dp</item>
        <item name="android:layout_marginBottom">20dp</item>
    </style>

    <!--Inspired from Rocket Northstar see :github[northstarui-android] for more Information  -->
    <style name="OrionButtonStyle" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textColor">@color/white</item>
        <item name="android:paddingRight">@dimen/space_normal</item>
        <item name="android:paddingLeft">@dimen/space_normal</item>
        <item name="android:layout_marginTop">@dimen/space_normal</item>
        <item name="android:layout_marginBottom">@dimen/space_normal</item>
        <item name="android:layout_marginRight">@dimen/space_normal</item>
        <item name="android:layout_marginLeft">@dimen/space_normal</item>
        <item name="android:height">@dimen/orion_button_height</item>
        <item name="android:maxWidth">@dimen/avito_max_button_width</item>
        <item name="backgroundTint">@color/colorPrimary</item>
        <item name="android:background">@drawable/button_primary</item>
        <item name="android:drawablePadding">@dimen/space_small</item>
        <item name="android:fontFamily">@font/rubik_medium</item>

        <item name="android:textSize">@dimen/orion_button_text_size</item>
        <item name="android:stateListAnimator" ns1:targetApi="lollipop">@null
        </item>
        <item name="android:paddingEnd" ns1:targetApi="jelly_bean_mr1">
            @dimen/space_normal
        </item>
        <item name="android:paddingStart" ns1:targetApi="jelly_bean_mr1">
            @dimen/space_normal
        </item>
    </style>
    <style name="OrionRoundButtonStyle" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/white</item>
        <item name="android:paddingRight">@dimen/space_normal</item>
        <item name="android:paddingLeft">@dimen/space_normal</item>
        <item name="android:layout_marginTop">@dimen/space_normal</item>
        <item name="android:layout_marginBottom">@dimen/space_normal</item>
        <item name="android:layout_marginRight">@dimen/space_normal</item>
        <item name="android:layout_marginLeft">@dimen/space_normal</item>
        <item name="android:height">@dimen/orion_button_height</item>
        <item name="android:maxWidth">@dimen/avito_max_button_width</item>
        <item name="backgroundTint">@color/colorPrimary</item>
        <item name="android:background">@drawable/button_primary</item>
        <item name="android:drawablePadding">@dimen/space_small</item>
        <item name="android:fontFamily">@font/rubik_medium</item>

        <item name="android:textSize">@dimen/orion_button_text_size</item>
        <item name="android:stateListAnimator" ns1:targetApi="lollipop">@null
        </item>
        <item name="android:paddingEnd" ns1:targetApi="jelly_bean_mr1">
            @dimen/space_normal
        </item>
        <item name="android:paddingStart" ns1:targetApi="jelly_bean_mr1">
            @dimen/space_normal
        </item>
    </style>

    <style name="OrionButtonDisabledStyle" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textColor">@color/white</item>
        <item name="android:paddingRight">@dimen/space_normal</item>
        <item name="android:paddingLeft">@dimen/space_normal</item>
        <item name="android:layout_marginTop">@dimen/space_normal</item>
        <item name="android:layout_marginBottom">@dimen/space_normal</item>
        <item name="android:layout_marginRight">@dimen/space_normal</item>
        <item name="android:layout_marginLeft">@dimen/space_normal</item>
        <item name="android:height">@dimen/orion_button_height</item>
        <item name="android:maxWidth">@dimen/avito_max_button_width</item>
        <item name="backgroundTint">@color/colorDisabled</item>
        <item name="android:background">@drawable/button_primary</item>
        <item name="android:drawablePadding">@dimen/space_small</item>
        <item name="android:fontFamily">@font/rubik_medium</item>

        <item name="android:textSize">@dimen/orion_button_text_size</item>
        <item name="android:stateListAnimator" ns1:targetApi="lollipop">@null
        </item>
        <item name="android:paddingEnd" ns1:targetApi="jelly_bean_mr1">
            @dimen/space_normal
        </item>
        <item name="android:paddingStart" ns1:targetApi="jelly_bean_mr1">
            @dimen/space_normal
        </item>
    </style>

    <style name="OrionButtonAlternativeStyle" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textColor">@color/black_grey</item>
        <item name="android:background">@drawable/button_secondary_background</item>
        <item name="android:paddingRight">@dimen/space_normal</item>
        <item name="android:paddingLeft">@dimen/space_normal</item>
        <item name="android:layout_marginTop">@dimen/space_normal</item>
        <item name="android:layout_marginBottom">@dimen/space_normal</item>
        <item name="android:layout_marginRight">@dimen/space_normal</item>
        <item name="android:layout_marginLeft">@dimen/space_normal</item>
        <item name="android:height">@dimen/orion_button_height</item>
        <item name="android:maxWidth">@dimen/avito_max_button_width</item>
        <item name="backgroundTint">@color/grey_listing_bg</item>
        <item name="android:drawablePadding">@dimen/space_small</item>
        <item name="android:fontFamily">@font/rubik_medium</item>

        <item name="android:textSize">@dimen/orion_button_text_size</item>
        <item name="android:stateListAnimator" ns1:targetApi="lollipop">@null
        </item>
        <item name="android:paddingEnd" ns1:targetApi="jelly_bean_mr1">
            @dimen/space_normal
        </item>
        <item name="android:paddingStart" ns1:targetApi="jelly_bean_mr1">
            @dimen/space_normal
        </item>
    </style>
    <!--Secondary Button Style .Inherit Properties from Avito primarily Button -->
    <style name="button_secondary" parent="OrionButtonStyle">
        <item name="android:background">@drawable/secondary_button</item>
        <item name="backgroundTint">@color/colorAccent</item>
    </style>

    <style name="OrionButtonSecondaryGrey" parent="OrionButtonStyle">
        <item name="android:fontFamily">@font/rubik_medium</item>
        <item name="android:textSize">@dimen/orion_text_normal</item>
        <item name="backgroundTint">@color/vas_highlighted_background</item>
        <item name="android:textColor">@color/black_grey</item>
    </style>

    <style name="cancelButtonStyle">
        <item name="borderlessButtonStyle">?android:attr/borderlessButtonStyle</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">42dp</item>
        <item name="android:background">@drawable/button_transparent</item>
        <item name="android:clickable">true</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textSize">@dimen/orion_button_text_size</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingLeft">18sp</item>
        <item name="android:paddingRight">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <!-- Title style -->
    <style name="TitleStyle">
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">#222</item>
        <item name="android:textSize">15sp</item>
    </style>
    <!-- SubTitle style -->
    <style name="SubTitleStyle">
        <item name="android:textColor">#444</item>
        <item name="android:textSize">13sp</item>

    </style>
    <!-- Orion : The new UI components library for Avito -->

    <style name="OrionInputTextError">
        <item name="android:textColor">#ec3535</item>
        <item name="android:textSize">12sp</item>
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="OrionEditText">
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/content_color</item>
        <item name="android:layout_height">@dimen/orion_input_field_height</item>
        <item name="android:textSize">13sp</item>
        <item name="android:background">@null</item>
        <item name="android:textColorHint">#868686</item>
        <item name="android:layout_marginLeft">8dp</item>
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_marginRight">0dp</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:paddingLeft">4dp</item>
        <item name="android:paddingStart">4dp</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:paddingRight">28dp</item>
        <item name="android:paddingEnd">28dp</item>

    </style>

    <style name="OrionAutocomplete">
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">#4a4a4a</item>
        <item name="android:textSize">16sp</item>
        <item name="android:background">@null</item>
        <item name="android:popupLayout">@drawable/spinner_bg</item>
        <item name="android:hint">@string/city_autocomplete_hint</item>
        <item name="android:textColorHint">@color/colorLightGray</item>
        <item name="android:layout_marginLeft">16dp</item>
        <item name="android:layout_marginRight">4dp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:paddingLeft">4dp</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:paddingRight">28dp</item>
    </style>

    <style name="OrionSimpleSpinner">
        <item name="android:dropDownHorizontalOffset">5dp</item>
        <item name="android:dropDownHeight">wrap_content</item>
        <item name="android:divider">@android:color/black</item>
        <item name="android:dividerHeight">1dp</item>
        <item name="android:spinnerMode">dropdown</item>
        <item name="android:background">@drawable/spinner_frame_normal</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:popupBackground">@android:color/white</item>
        <item name="android:textColor">@color/content_color</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">13sp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:paddingLeft">4dp</item>
        <item name="android:paddingRight">28dp</item>

    </style>

    <style name="OrionIconField">
        <item name="android:layout_width">@dimen/orion_icon_field_size</item>
        <item name="android:layout_height">@dimen/orion_icon_field_size</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_marginLeft">8dp</item>
        <item name="android:layout_marginEnd">4dp</item>
        <item name="android:layout_marginRight">4dp</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="OrionIconAction">
        <item name="android:layout_width">@dimen/orion_icon_action_size</item>
        <item name="android:layout_height">@dimen/orion_icon_action_size</item>
        <item name="android:padding">4dp</item>
        <item name="android:tint">@color/orionLightGrey</item>
    </style>
    <!-- Navigation View Item -->
    <style name="navigationViewMenuItemStyle" parent="android:Widget.TextView">
        <item name="android:textSize">@dimen/text_size_menu_item</item>
        <item name="android:textColor">@color/avito_primary_text_color</item>
        <item name="android:fontFamily">@font/rubik</item>
    </style>


    <style name="MessageBubble">
        <item name="android:paddingLeft">@dimen/default_spacing</item>
        <item name="android:paddingRight">@dimen/default_spacing</item>
        <item name="android:paddingStart">@dimen/default_spacing</item>
        <item name="android:paddingEnd">@dimen/default_spacing</item>
        <item name="android:paddingTop">@dimen/half_spacing</item>
        <item name="android:paddingBottom">@dimen/half_spacing</item>
    </style>

    <style name="ChooseVasPackageScreenTitleStyle">
        <item name="fontFamily">@font/rubik_bold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/darkerBlue</item>
        <item name="android:textSize">@dimen/orion_text_medium</item>
    </style>

    <style name="ChooseVasPackageScreenDescriptionStyle">
        <item name="fontFamily">@font/rubik</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/mediumLightBlue</item>
        <item name="android:textSize">@dimen/orion_text_small</item>
    </style>

    <style name="ChooseVasPackageScreenLocationStyle">
        <item name="fontFamily">@font/rubik</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/mediumLightBlue</item>
        <item name="android:textSize">@dimen/orion_text_small</item>
    </style>

    <style name="ChooseVasPackageScreenCategoryLabelStyle">
        <item name="fontFamily">@font/rubik</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/darkerBlue</item>
        <item name="android:textSize">@dimen/orion_text_normal</item>
    </style>

    <style name="IconButton" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:layout_width">42dp</item>
        <item name="android:layout_height">42dp</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:text">""</item>
        <item name="rippleColor">?attr/colorControlHighlight</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="iconGravity">textStart</item>
        <item name="iconPadding">0dp</item>
        <item name="iconTint">?attr/colorOnBackground</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.App.CornerSize50Percent</item>
    </style>

    <style name="GalleryButtonStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/bg_rect_gray</item>
        <item name="android:backgroundTint">@color/black</item>
        <item name="drawableTint">@color/white</item>
        <item name="android:drawablePadding">2dp</item>
        <item name="android:fontFamily">@font/rubik_medium</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:padding">4dp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">@dimen/ef_font_small</item>
        <item name="android:maxLines">1</item>
        <item name="android:layout_marginEnd">@dimen/space_tiny</item>
    </style>
    <style name="TabIndicatorStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/margin_small_plus</item>
        <item name="android:backgroundTint">@android:color/transparent</item>
        <item name="tabBackground">@drawable/selector_tab_indicator</item>
        <item name="tabGravity">center</item>
        <item name="tabIndicatorHeight">0dp</item>
        <item name="tabMaxWidth">@dimen/margin_small_plus</item>
        <item name="tabMode">scrollable</item>
        <item name="tabPaddingEnd">@dimen/margin_small</item>
        <item name="tabPaddingStart">@dimen/margin_small</item>
    </style>

</resources>
