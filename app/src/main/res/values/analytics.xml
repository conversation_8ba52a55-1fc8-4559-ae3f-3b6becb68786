<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- AMPLITUDE -->
    <!-- QA-->
    <!-- <string name="amplitude_api_key" translatable="false">********************************</string>-->
    <!-- PROD-->
    <string name="amplitude_api_key" translatable="false">********************************</string>


    <!-- GCM -->
    <string name="GCM_SENDER_ID">647240640772</string>


    <string name="am_event_visited_ad">Visited Ad</string>
    <string name="am_event_visited_orders">Visited My Orders</string>
    <string name="am_event_visited_orders_cancel">cancelorder</string>
    <string name="am_event_visited_orders_discover_products">discover_products</string>
    <string name="am_event_visited_orders_status">select_status</string>
    <string name="am_event_visited_orders_clicked">clicked_my_orders</string>

    <string name="am_event_visited_ad_gallery">Visited Ad Gallery</string>
    <string name="am_event_visited_ad_checkout">checkout</string>
    <string name="am_event_payment">payment</string>

    <string name="am_event_visited_listing">Visited Listing</string>
    <string name="am_event_trigger_ad_insert">Trigger Ad Insert</string>
    <string name="am_event_trigger_speech_to_text">Trigger Speech to Text</string>
    <string name="am_event_trigger_drag_drop">Image drag and drop</string>
    <string name="am_event_ad_insert_step_x">Completed Insert Ad Step %1$d</string>
    <string name="am_event_sign_up">User Sign Up</string>
    <string name="am_event_sign_in">User Sign In</string>
    <string name="am_event_open_search">Open Search</string>
    <string name="am_event_submited_search">Submited Search</string>
    <string name="am_event_send_sms">Send SMS</string>
    <string name="am_event_call_seller">Call Seller</string>
    <string name="am_event_send_message">Send Message</string>
    <string name="am_event_send_ad_reply">Send Ad Reply</string>
    <string name="am_event_ad_reply_sent">Ad Reply Sent</string>
    <string name="am_event_bump_pressed">Bump Ad Pressed</string>
    <string name="am_event_bumped_ad">Bumped Ad</string>
    <string name="am_event_favorited_ad">Favorited Ad</string>
    <string name="am_event_favorited_ad_drawer">Clicked MyfavoriteDrawerMenu</string>
    <string name="am_event_saved_search">saved search</string>
    <string name="am_event_clicked_zoom">Clicked on zoom</string>
    <string name="am_event_clicked_wafasalaf">Clicked wafasalaf</string>
    <string name="am_event_viewed_wafasalaf">Viewed wafasalaf</string>


    <!-- event propreties -->
    <string name="am_property_favorite_view_mode">Favorite View Mode</string>
    <string name="am_property_search_result_count">ResultCount</string>
    <string name="am_property_search_result_offset">PageNumber</string>
    <string name="am_property_source">Source</string>
    <string name="am_property_main_category">MainCategory</string>
    <string name="am_property_sub_category">SubCategory</string>
    <string name="am_property_pictures_count">PicturesCount</string>
    <string name="am_property_has_price">HasPrice</string>
    <string name="am_property_city">City</string>
    <string name="am_user_property_name">Name</string>
    <string name="am_user_property_email">Email</string>
    <string name="am_user_property_phone">Phone</string>
    <string name="am_property_price">Price</string>
    <string name="am_property_package_id">PackageId</string>
    <string name="am_property_ad_list_id">ListId</string>
    <string name="am_stt_trigger">Triggered from</string>
    <string name="am_property_stt_adi_title">title</string>
    <string name="am_property_stt_adi_description">description</string>
    <string name="am_image_drag_trigger">Image dragged</string>


    <string name="am_property_mode">Mode</string>

    <!--event values-->
    <string name="am_screen_listing">Listing</string>
    <string name="am_screen_ad_view">AdView</string>
    <string name="am_mode_list">List</string>

    <!-- screen views -->
    <string name="tm_screen_listing">AdsListing</string>
    <string name="tm_screen_ad_view">AdView</string>
    <string name="tm_screen_ad_gallery">AdGallery</string>
    <string name="tm_screen_side_menu">LeftMenu</string>
    <string name="tm_screen_search">SearchFilters</string>
    <string name="tm_screen_ad_insert_step_x">AdInsert%1$d</string>
    <string name="tm_screen_ad_insert_gallery">AdInsertGallery</string>
    <string name="tm_screen_saved_searches">SavedSearches</string>
    <string name="tm_screen_favorite_ads">FavoriteAds</string>
    <string name="tm_screen_infos_screen">InfoScreen</string>
    <string name="tm_screen_ad_reply_form">AdReplyForm</string>
    <string name="tm_screen_ad_bump_init">AdBumpInitDialog</string>
    <string name="tm_screen_ad_bump_success">AdBumpSuccessDialog</string>
    <string name="tm_screen_my_ads">MyAds</string>
    <string name="tm_screen_my_account">MyAccount</string>

    <!-- conversions -->
    <string name="tm_conversion_ad_reply">adReply</string>
    <string name="tm_conversion_call_seller">showPhoneNumber</string>
    <string name="tm_conversion_sms_seller">smsSent</string>
    <string name="tm_conversion_ad_inserted">adInserted</string>
    <string name="tm_conversion_ad_updated">adUpdated</string>

    <!-- custom events -->
    <string name="tm_event_ad_reply_pressed">adReplyPressed</string>
    <string name="tm_event_bump_init">adBumpInit</string>
    <string name="tm_event_bump_send_sms">adBumpSendSms</string>
    <string name="tm_event_favourited_ad">favouritedAd</string>
    <string name="tm_event_saved_search">savedSearch</string>
    <string name="tm_event_submitted_search">submittedSearch</string>
    <string name="tm_event_ad_gallery_pressed">adGalleryPressed</string>
    <string name="tm_event_ad_insert_triggered">adInsertTrigger</string>
    <string name="tm_event_edit_account_pressed">editAccount</string>
    <string name="tm_event_edit_ad_pressed">editAd</string>
    <string name="tm_event_ad_management_pressed">clickedAdManagement</string>
    <string name="tm_event_share_ad_pressed">clickedShareAd</string>
    <string name="tm_event_delete_ad_pressed">clickedDeleteAd</string>
    <string name="tm_event_activate_ad_pressed">clickedActivatedAd</string>
    <string name="tm_event_deactivate_ad_pressed">clickedDeactivatedAd</string>
    <string name="event_ad_activated">AdActivated</string>
    <string name="event_ad_deactivated">AdDeactivated</string>
    <string name="event_ad_deleted">AdDeleted</string>
    <string name="tm_event_my_ads_tab_visited">myAdsTabVisited</string>
    <string name="tm_event_my_active_ads_tab_visited">MyActiveAdsVisited</string>
    <string name="tm_event_my_inactive_ads_tab_visited">MyInactiveAdsVisited</string>
    <string name="tm_event_my_refused_ads_tab_visited">MyRefusedAdsVisited</string>
    <string name="tm_event_my_deleted_ads_tab_visited">MyDeletedAdsVisited</string>
    <string name="tm_event_messaging_tab_visited">MessagingVisited</string>
    <string name="tm_event_notification_tab_visited">NotificationCenterVisited</string>
    <string name="tm_event_my_account_tab_visited">MyAccountVisited</string>
    <string name="tm_event_scroll_gridlisting">pictureScrolledGridListing</string>
    <string name="tm_event_switch_to_arabic">switchedToArabic</string>
    <string name="tm_event_switch_to_french">switchedToFrench</string>
    <string name="tm_event_switch_to_arabic_popup">switchedToArabicPopup</string>
    <string name="tm_event_switch_to_french_popup">switchedToFrenchPopup</string>
    <!-- google tag manager -->
    <string name="gtm_open_screen">screenView</string>
    <string name="gtm_button_press">buttonPress</string>
    <string name="gtm_list_press">listPress</string>
    <string name="gtm_list_label">listLabel</string>
    <string name="gtm_list_item_select">itemTap</string>
    <string name="gtm_selected_item">selectedItem</string>
    <string name="gtm_screen_label">screenName</string>
    <string name="gtm_control_label">controlName</string>
    <string name="gtm_swipe_right_event">SwipeRight</string>
    <string name="gtm_swipe_left_event">SwipeLeft</string>

    <string name="gtm_event_show_phone">showPhoneNumber</string>
    <string name="gtm_event_sms_sent">smsSent</string>
    <string name="gtm_event_ad_reply">adReply</string>
    <string name="gtm_event_ad_inserted">adInserted</string>
    <string name="gtm_event_ad_bump_init">adBumpInit</string>
    <string name="gtm_event_ad_bump_send_sms">adBumpSendSms</string>

    <!-- Apteligent userflows -->
    <string name="crittercism_bump_init">AdBumpInit</string>
    <string name="crittercism_bump_confirm">AdBump</string>
    <string name="crittercism_ad_insert">AdInsert</string>
    <string name="crittercism_ad_update">AdUpdate</string>
    <string name="crittercism_sign_in">SignIn</string>
    <string name="crittercism_sign_up">SignUp</string>
    <string name="crittercism_ad_reply">AdReply</string>
    <string name="crittercism_call_seller">CallSeller</string>
    <string name="crittercism_sms_seller">SmsSeller</string>

    <!-- static  property value-->
    <string name="am_val_ad_view">adView</string>
    <string name="am_val_my_account">myAccount</string>
    <string name="am_val_listing">listing</string>
    <string name="am_val_favorite">favorite</string>
    <string name="am_val_deep_link">deeplink</string>
    <string name="am_val_list">List</string>
    <string name="am_val_cash_payment_success">Cash Payment Success Page</string>
    <string name="am_val_cc_payment_receipt">Credit card Payment Success Page</string>
    <string name="am_val_sms_payment_success">SMS Payment Success Page</string>
    <string name="an_vas_category">Vas Category</string>

    <!--payment method-->
    <string name="an_event_vas_sms">SMS</string>
    <string name="an_event_vas_credit_card">Credit Card</string>
    <string name="an_event_vas_cash_payment">Cash</string>

    <!--Ad Insert -->
    <string name="am_event_ad_insert_successful">Inserted Ad successfully</string>
    <string name="am_property_vas_ai_user_chose_package">isUserChosePackage</string>
    <string name="am_property_vas_ai_package_id">PackageId</string>
    <string name="canceled_ad_insert">Cancelled ad insert</string>
    <string name="clicked_on_cancel_ad_insert">Clicked on cancel ad insert</string>
    <string name="clicked_on_return_to_previous_step">Clicked on return to step %1$s</string>

    <!--VAS-->
    <string name="an_event_click_on_vas">Requested VAS</string>
    <string name="an_event_click_on_sms_shortcut">Requested VAS SMS Shortcut</string>
    <string name="an_event_choose_vas_category">Chose VAS Category</string>
    <string name="an_event_choose_vas_package">Chose VAS Package</string>

    <!--Choose Vas Payment Method-->
    <string name="an_event_choose_vas_payment_method">Vas Payment Method</string>
    <string name="an_vas_payment_method">Chose Payment Method</string>

    <!--SMS-->
    <string name="an_event_click_on_cta_sms_send">Ad Bump Send SMS</string>
    <string name="an_event_success_page_sms_showned">Success SMS Page</string>

    <!--FORM-->
    <string name="an_event_send_filled_form">Sent Payment Form</string>
    <string name="an_event_form_payment_type">Payment Method</string>

    <string name="an_event_send_filled_payment_page_credit">Sent Payzone WebWiew Form</string>
    <string name="an_event_click_screenshot_cta">Took Payment Code Screenshot</string>

    <string name="an_event_listing_page">ReturnListingPage</string>

    <!--VAS Screens-->
    <string name="an_screen_vas_category">VasCategoryScreen</string>
    <string name="an_screen_vas_package">VasPackageScreen</string>
    <string name="an_screen_vas_payment_method">VasPaymentMethodScreen</string>
    <string name="an_screen_vas_sms_payment">VasSmsPaymentSuccessScreen</string>
    <string name="an_screen_vas_form">VasFormScreen</string>
    <string name="an_screen_receipt">VasReceiptScreen</string>
    <string name="an_screen_vas_cc_payment">VasCCPaymentScreen</string>
    <string name="an_screen_ecommerce_cc_payment">EcommerceCCPaymentScreen</string>

    <string name="an_screen_vas_cash_payment">VasCashPaymentScreen</string>
    <string name="an_screen_vas_success">VasSmsPaymentScreen</string>

    <!--Receipt-->
    <string name="an_event_receipt_screenshot_cta">Took Receipt Screenshot</string>

    <!--MEDIA-->

    <string name="an_screen_media_webview">MediaWebViewScreen</string>
    <string name="fcm_sender_id">647240640772</string>

    <!--Messaging -->
    <string name="clicked_on_inbox">Clicked on Inbox</string>
    <string name="viewed_login_prompter">Viewed login prompter</string>
    <string name="clicked_on_chat_cta">Clicked on chat CTA</string>
    <string name="sent_message">Sent message</string>
    <string name="clicked_on_chat_cta_ad_view">adView</string>
    <string name="clicked_on_chat_cta_image_carousel">Image-carousel</string>
    <string name="view_mode">ViewMod</string>
    <string name="displayed_job_application_form">displayed job application form</string>
    <string name="applied_to_job_application">applied to job application</string>
    <string name="vas_listing_popup_display">Pop up Listing Fees Displayed</string>
    <string name="vas_listing_overpassed_display">Listing Fees Reminder Displayed</string>

    <!--Recommendation Ads-->
    <string name="amp_clicked_recommendation_widget">clicked Recommendation widget</string>
    <string name="amp_viewed_recommendation_widget">Viewed recommendation widget</string>
    <string name="amp_loaded_recommendation_widget">Loaded Recommendation widget</string>
    <string name="amp_source">Source</string>
    <string name="amp_source_messaging">Messaging</string>
    <string name="amp_source_adview">AdView</string>
    <string name="amp_source_deeplink">Deeplink</string>

    <!--More Ads by the same user-->
    <string name="amp_clicked_more_ads_widget">Clicked more ads widget</string>
    <string name="amp_viewed_more_ads_widget">Viewed more ads widget</string>
    <string name="clicked_on_mau_link">Clicked on more ads by the same user link</string>
    <string name="refreshed_ads_user_profile">Refreshed user ads in user profile</string>
    <string name="loaded_more_ads_user_profile">Loaded more ads in user profile</string>
    <string name="clicked_on_back_to_ad_view">Clicked on back to ad view</string>
    <string name="clicked_on_back_from_deeplink">Clicked on back from deeplink</string>
    <string name="clicked_on_sort_option">Clicked on sort ads in user profile</string>
    <string name="am_event_sign_in_using_facebook">User logged in using facebook</string>
    <string name="am_event_sign_in_using_google">User logged in using google</string>

    <!--Draft Ad Insert-->
    <string name="draft_ad_banner">Clicked drafted Ad banner</string>
    <string name="loaded_draft_ai">Loaded drafted Ad</string>
    <string name="saved_draft_ad">Saved Draft Ad</string>


</resources>
